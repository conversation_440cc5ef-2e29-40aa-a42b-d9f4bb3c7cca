package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.service.IdentityService;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.POST_PARTNER_HUB_USER_REGISTRATION;

/**
 * Processor that authenticates the user by retrieving their profile.
 *
 * <AUTHOR>
 */
@Component
public class AuthenticationProcessor extends BaseIdentityProcessor {

    private final IdentityService identityService;

    public AuthenticationProcessor(IdentityService identityService, ResponseUtils responseUtils) {
        super(responseUtils);
        this.identityService = identityService;
    }

    @Override
    protected int getDefaultOrder() {
        return 50; // Execute after UserLogoutProcessor (41)
    }

    // init() method inherited from BaseIdentityProcessor

    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        // Skip authentication for the user profile endpoint to avoid circular dependency
        return (shouldProcessBasedOnOperation(context, GET_LOGGED_IN_USER_PROFILE, false) ||
        shouldProcessBasedOnOperation(context, POST_PARTNER_HUB_USER_REGISTRATION, false));
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpHeaders headers = context.getHeaders();
        Resource resource = context.getResource();

        LoggingUtils.debug("Authenticating user for resource: {}", resource.getOperation());

        return identityService.getUserProfile(headers)
                .flatMap(userProfile -> {
                    context.setUserProfile(userProfile);
                    return Mono.just(ProcessingResult.continueProcessing());
                })
                .onErrorResume(throwable -> {
                    LoggingUtils.error("Authentication failed: {}", throwable.getMessage());
                    return handleWebClientError(headers, resource)
                            .map(ProcessingResult::terminate);
                });
    }

    /**
     * Handles errors that occur during authentication.
     *
     * @param headers  The request headers
     * @param resource The resource being processed
     * @return A Mono that emits a response entity with an appropriate error message
     */
    private Mono<ResponseEntity<byte[]>> handleWebClientError(HttpHeaders headers, Resource resource) {
        Mono<ResponseEntity<byte[]>> result = createErrorResponse(headers, resource, HttpStatus.UNAUTHORIZED, "GW_ERR7", "Authentication failed");
        return result != null ? result : Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).build());
    }
}
